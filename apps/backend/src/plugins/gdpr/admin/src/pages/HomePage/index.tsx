import React from "react";
import {
  HeaderLayout,
  ContentLayout,
  Box,
  Typography,
} from "@strapi/design-system";

const HomePage = () => {
  return (
    <>
      <HeaderLayout
        title="GDPR Compliance"
        subtitle="Manage data privacy, user requests, and compliance settings"
      />
      <ContentLayout>
        <Box padding={8} background="neutral0" hasRadius shadow="tableShadow">
          <Typography variant="alpha">
            GDPR Compliance Plugin
          </Typography>
          <Typography variant="epsilon">
            This plugin helps manage data privacy, user requests, and compliance settings according to GDPR regulations.
          </Typography>

          <Box paddingTop={6}>
            <Typography variant="beta" as="h3">
              Features
            </Typography>
            <Box paddingTop={2}>
              <Typography variant="omega">
                • Data export requests management
              </Typography>
              <Typography variant="omega">
                • Data deletion and anonymization
              </Typography>
              <Typography variant="omega">
                • Consent tracking and management
              </Typography>
              <Typography variant="omega">
                • Audit logging for compliance
              </Typography>
            </Box>
          </Box>
        </Box>
      </ContentLayout>
    </>
  );
};

export default HomePage;