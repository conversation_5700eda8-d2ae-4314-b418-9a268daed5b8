{"name": "@civicpoll/strapi-plugin-audit-log", "version": "0.0.0", "description": "Strapi plugin for comprehensive audit logging", "type": "commonjs", "exports": {"./package.json": "./package.json", "./strapi-admin": {"source": "./admin/src/index.tsx", "import": "./dist/admin/index.mjs", "require": "./dist/admin/index.js", "default": "./dist/admin/index.js"}, "./strapi-server": {"source": "./server/index.ts", "import": "./dist/server/index.mjs", "require": "./dist/server/index.js", "default": "./dist/server/index.js"}}, "files": ["dist"], "scripts": {"build": "strapi-plugin build", "watch": "strapi-plugin watch", "watch:link": "strapi-plugin watch:link", "verify": "strapi-plugin verify"}, "strapi": {"name": "@civicpoll/strapi-plugin-audit-log", "description": "Log and track all important system events with search and retention policies", "kind": "plugin", "displayName": "<PERSON>t Log"}, "dependencies": {"@strapi/design-system": "^2.0.0-rc.27", "@strapi/icons": "^2.0.0-rc.27", "react-intl": "^7.1.11"}, "devDependencies": {"@strapi/strapi": "^5.16.1", "@strapi/sdk-plugin": "^5.3.2", "prettier": "^3.6.2", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^6.30.1", "styled-components": "^6.1.19", "typescript": "^5.0.0"}, "peerDependencies": {"@strapi/strapi": "^5.16.1", "@strapi/sdk-plugin": "^5.3.2", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^6.30.1", "styled-components": "^6.1.19"}, "author": {"name": "CivicPoll Team"}, "maintainers": [{"name": "CivicPoll Team"}], "engines": {"node": ">=18.0.0", "npm": ">=6.0.0"}, "license": "MIT"}