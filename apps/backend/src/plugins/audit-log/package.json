{"name": "audit-log", "version": "0.0.0", "description": "Strapi plugin for comprehensive audit logging", "strapi": {"name": "audit-log", "description": "Log and track all important system events with search and retention policies", "kind": "plugin", "displayName": "<PERSON>t Log"}, "dependencies": {"@strapi/design-system": "^1.6.3", "@strapi/helper-plugin": "^4.6.0", "@strapi/icons": "^1.6.3", "prop-types": "^15.7.2"}, "peerDependencies": {"@strapi/strapi": "^4.0.0"}, "author": {"name": "CivicPoll Team"}, "maintainers": [{"name": "CivicPoll Team"}], "engines": {"node": ">=14.19.1 <=18.x.x", "npm": ">=6.0.0"}, "license": "MIT"}