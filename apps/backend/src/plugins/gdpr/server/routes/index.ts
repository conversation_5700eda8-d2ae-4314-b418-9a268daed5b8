export default [
    {
        method: "GET",
        path: "/export/:userId",
        handler: "gdpr.exportData",
        config: {
            policies: ["plugin::gdpr.is-owner-or-admin"],
            auth: {
                scope: ["plugin::gdpr.access"]
            },
        },
    },
    {
        method: "DELETE",
        path: "/delete/:userId",
        handler: "gdpr.deleteData",
        config: {
            policies: ["plugin::gdpr.is-owner-or-admin"],
            auth: {
                scope: ["plugin::gdpr.access"]
            },
        },
    },
    {
        method: "POST",
        path: "/anonymize/:userId",
        handler: "gdpr.anonymizeData",
        config: {
            policies: ["plugin::gdpr.is-owner-or-admin"],
            auth: {
                scope: ["plugin::gdpr.access"]
            },
        },
    },
    {
        method: "GET",
        path: "/consents/:userId",
        handler: "gdpr.getConsents",
        config: {
            policies: ["plugin::gdpr.is-owner-or-admin"],
            auth: {
                scope: ["plugin::gdpr.access"]
            },
        },
    },
    {
        method: "PUT",
        path: "/consents/:userId",
        handler: "gdpr.updateConsents",
        config: {
            policies: ["plugin::gdpr.is-owner-or-admin"],
            auth: {
                scope: ["plugin::gdpr.access"]
            },
        },
    },
    {
        method: "GET",
        path: "/audit-logs",
        handler: "gdpr.getAuditLogs",
        config: {
            policies: ["admin::isAuthenticatedAdmin"],
            auth: {
                scope: ["plugin::gdpr.access"]
            },
        },
    },
    {
        method: "GET",
        path: "/request/:requestId",
        handler: "gdpr.getRequestStatus",
        config: {
            policies: [],
            auth: {
                scope: ["plugin::gdpr.access"]
            },
        },
    },
    {
        method: "GET",
        path: "/audit-logs/export",
        handler: "gdpr.exportAuditLogs",
        config: {
            policies: ["admin::isAuthenticatedAdmin"],
            auth: {
                scope: ["plugin::gdpr.access"]
            },
        },
    },
    {
        method: "GET",
        path: "/statistics",
        handler: "gdpr.getStatistics",
        config: {
            policies: ["admin::isAuthenticatedAdmin"],
            auth: {
                scope: ["plugin::gdpr.access"]
            },
        },
    },
];
