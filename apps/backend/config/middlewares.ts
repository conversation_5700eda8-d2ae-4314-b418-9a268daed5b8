export default ({ env }) => [
    "strapi::logger",
    "strapi::errors",
    {
        name: "strapi::security",
        config: {
            contentSecurityPolicy: {
                useDefaults: true,
                directives: {
                    "connect-src": ["'self'", "https:"],
                    "img-src": ["'self'", "data:", "blob:", "market-assets.strapi.io"],
                    "media-src": ["'self'", "data:", "blob:", "market-assets.strapi.io"],
                    upgradeInsecureRequests: null,
                },
            },
        },
    },
    {
        name: "strapi::cors",
        config: {
            enabled: true,
            headers: "*",
            origin: env("FRONTEND_URL", "http://localhost:3000").split(","),
        },
    },
    "strapi::poweredBy",
    "strapi::query",
    {
        name: "strapi::body",
        config: {
            formLimit: "256mb",
            jsonLimit: "256mb",
            textLimit: "256mb",
            formidable: {
                maxFileSize: 250 * 1024 * 1024, // 250mb
            },
        },
    },
    "strapi::session",
    "strapi::favicon",
    "strapi::public",
];
