import React from "react";
import {
  Page,
  HeaderLayout,
  ContentLayout,
  Box,
  Grid,
  Typography,
  Tabs,
  Tab,
  TabGroup,
  TabPanels,
  TabPanel,
} from "@strapi/design-system";
import { Shield, Database, FileText, Settings } from "@strapi/icons";

const HomePage = () => {
  return (
    <Layout>
      <BaseHeaderLayout
        title="GDPR Compliance"
        subtitle="Manage data privacy, user requests, and compliance settings"
        as="h2"
      />
      <ContentLayout>
        <Box padding={8} background="neutral0" hasRadius shadow="tableShadow">
          <TabGroup label="GDPR Management" variant="simple">
            <Tabs>
              <Tab>
                <Shield />
                Overview
              </Tab>
              <Tab>
                <Database />
                Data Requests
              </Tab>
              <Tab>
                <FileText />
                Audit Logs
              </Tab>
              <Tab>
                <Settings />
                Settings
              </Tab>
            </Tabs>
            <TabPanels>
              <TabPanel>
                <Box padding={4}>
                  <Typography variant="beta" as="h3">
                    GDPR Compliance Overview
                  </Typography>
                  <Box paddingTop={4}>
                    <Grid gap={6}>
                      <GridItem col={4}>
                        <Box padding={4} background="primary100" hasRadius>
                          <Typography variant="omega" fontWeight="bold">
                            Data Exports
                          </Typography>
                          <Typography variant="alpha">0</Typography>
                          <Typography variant="pi" textColor="neutral600">
                            Total export requests
                          </Typography>
                        </Box>
                      </GridItem>
                      <GridItem col={4}>
                        <Box padding={4} background="secondary100" hasRadius>
                          <Typography variant="omega" fontWeight="bold">
                            Data Deletions
                          </Typography>
                          <Typography variant="alpha">0</Typography>
                          <Typography variant="pi" textColor="neutral600">
                            Total deletion requests
                          </Typography>
                        </Box>
                      </GridItem>
                      <GridItem col={4}>
                        <Box padding={4} background="success100" hasRadius>
                          <Typography variant="omega" fontWeight="bold">
                            Active Consents
                          </Typography>
                          <Typography variant="alpha">0</Typography>
                          <Typography variant="pi" textColor="neutral600">
                            Users with active consents
                          </Typography>
                        </Box>
                      </GridItem>
                    </Grid>
                  </Box>
                </Box>
              </TabPanel>
              <TabPanel>
                <Box padding={4}>
                  <Typography variant="beta" as="h3">
                    Data Request Management
                  </Typography>
                  <Typography variant="omega" textColor="neutral600">
                    View and manage user data requests including exports, deletions, and anonymization.
                  </Typography>
                </Box>
              </TabPanel>
              <TabPanel>
                <Box padding={4}>
                  <Typography variant="beta" as="h3">
                    Audit Logs
                  </Typography>
                  <Typography variant="omega" textColor="neutral600">
                    Track all GDPR-related activities and data access logs.
                  </Typography>
                </Box>
              </TabPanel>
              <TabPanel>
                <Box padding={4}>
                  <Typography variant="beta" as="h3">
                    GDPR Settings
                  </Typography>
                  <Typography variant="omega" textColor="neutral600">
                    Configure data retention policies, consent types, and compliance settings.
                  </Typography>
                </Box>
              </TabPanel>
            </TabPanels>
          </TabGroup>
        </Box>
      </ContentLayout>
    </Layout>
  );
};

export default HomePage;